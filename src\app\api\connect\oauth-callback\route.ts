import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { doc, setDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state'); // This contains the userId
    const error = searchParams.get('error');

    // Handle OAuth errors
    if (error) {
      const errorDescription = searchParams.get('error_description') || 'OAuth authentication failed';
      console.error('OAuth error:', error, errorDescription);
      
      const origin = req.headers.get('origin') || 'http://localhost:3000';
      return NextResponse.redirect(
        `${origin}/payment?error=${encodeURIComponent(errorDescription)}&tab=connect`
      );
    }

    if (!code || !state) {
      return NextResponse.json(
        { error: 'Missing authorization code or user state' },
        { status: 400 }
      );
    }

    const userId = state; // The state parameter contains our user ID
    console.log('Processing OAuth callback for user:', userId, 'with code:', code);

    // Exchange authorization code for access token and account info
    const response = await stripe.oauth.token({
      grant_type: 'authorization_code',
      code: code,
    });

    const {
      access_token,
      refresh_token,
      token_type,
      stripe_publishable_key,
      stripe_user_id,
      scope
    } = response;

    console.log('OAuth token exchange successful for account:', stripe_user_id);

    // Get detailed account information
    const account = await stripe.accounts.retrieve(stripe_user_id);

    // Save account details to Firebase
    try {
      const { db } = await initFirebase();
      const sellerRef = doc(db, 'sellers', userId);
      
      await setDoc(sellerRef, {
        stripeAccountId: stripe_user_id,
        accessToken: access_token,
        refreshToken: refresh_token,
        publishableKey: stripe_publishable_key,
        tokenType: token_type,
        scope: scope,
        email: account.email,
        businessName: account.business_profile?.name,
        connectedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        onboardingComplete: account?.details_submitted || false,
        chargesEnabled: account?.charges_enabled || false,
        payoutsEnabled: account?.payouts_enabled || false,
        country: account.country,
        currency: account.default_currency,
        accountType: account.type,
        businessType: account.business_type
      }, { merge: true });

      console.log('Saved OAuth account details to Firebase:', { 
        userId, 
        accountId: stripe_user_id,
        email: account.email 
      });
    } catch (firebaseError) {
      console.error('Error saving OAuth details to Firebase:', firebaseError);
      // Continue with redirect even if Firebase save fails
    }

    // Redirect back to payment page with success message and account details
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    const redirectUrl = new URL(`${origin}/payment`);
    redirectUrl.searchParams.set('connected', 'true');
    redirectUrl.searchParams.set('account', stripe_user_id);
    redirectUrl.searchParams.set('userId', userId);
    redirectUrl.searchParams.set('tab', 'connect');
    redirectUrl.searchParams.set('email', account.email || '');
    redirectUrl.searchParams.set('business', account.business_profile?.name || '');

    return NextResponse.redirect(redirectUrl.toString());

  } catch (error) {
    console.error('Error in OAuth callback:', error);
    
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    return NextResponse.redirect(
      `${origin}/payment?error=${encodeURIComponent('Failed to complete login')}&tab=connect`
    );
  }
}
